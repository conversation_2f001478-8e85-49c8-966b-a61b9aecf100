// ignore_for_file: avoid_print

import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import '../widgets/home/<USER>';
import '../utils/debouncer.dart';

class Post {
  final String id;
  final String author;
  final String authorId;
  final String? title;
  final String content;
  final double price;
  final String category;
  final List<String> tags;
  final DateTime createdAt;
  final int likes;
  final int views;
  final bool isPaid;
  final bool isPublic;
  final bool allowComments;
  final String? imageUrl; // Keep for backward compatibility
  final List<String> imageUrls; // Multiple images
  final List<String> videoUrls; // Multiple videos
  final String? linkUrl;
  final List<String> likedBy; // Users who liked this post
  // Poll-related fields
  final bool hasPoll; // Whether this post has a poll
  final Map<String, int>
      pollVotes; // Vote counts for each option: {"yes": 5, "no": 3, "dont_care": 2}
  final Map<String, List<String>> pollVoters; // Users who voted for each option

  Post({
    required this.id,
    required this.author,
    required this.authorId,
    this.title,
    required this.content,
    required this.price,
    required this.category,
    this.tags = const [],
    required this.createdAt,
    this.likes = 0,
    this.views = 0,
    this.isPaid = false,
    this.isPublic = true,
    this.allowComments = true,
    this.imageUrl,
    this.imageUrls = const [],
    this.videoUrls = const [],
    this.linkUrl,
    this.likedBy = const [],
    this.hasPoll = false,
    this.pollVotes = const {},
    this.pollVoters = const {},
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'author': author,
      'authorId': authorId,
      'title': title,
      'content': content,
      'price': price,
      'category': category,
      'tags': tags,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'likes': likes,
      'views': views,
      'isPaid': isPaid,
      'isPublic': isPublic,
      'allowComments': allowComments,
      'imageUrl': imageUrl,
      'imageUrls': imageUrls,
      'videoUrls': videoUrls,
      'linkUrl': linkUrl,
      'likedBy': likedBy,
      'hasPoll': hasPoll,
      'pollVotes': pollVotes,
      'pollVoters': pollVoters,
    };
  }

  static Post fromMap(Map<String, dynamic> map) {
    return Post(
      id: map['id'],
      author: map['author'],
      authorId: map['authorId'],
      title: map['title'],
      content: map['content'],
      price: map['price'].toDouble(),
      category: map['category'],
      tags: List<String>.from(map['tags'] ?? []),
      createdAt: map['createdAt'] is int
          ? DateTime.fromMillisecondsSinceEpoch(map['createdAt'])
          : (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      likes: map['likes'] ?? 0,
      views: map['views'] ?? 0,
      isPaid: map['isPaid'] ?? false,
      isPublic: map['isPublic'] ?? true,
      allowComments: map['allowComments'] ?? true,
      imageUrl: map['imageUrl'],
      imageUrls: List<String>.from(map['imageUrls'] ?? []),
      videoUrls: List<String>.from(map['videoUrls'] ?? []),
      linkUrl: map['linkUrl'],
      likedBy: List<String>.from(map['likedBy'] ?? []),
      hasPoll: map['hasPoll'] ?? false,
      pollVotes: Map<String, int>.from(map['pollVotes'] ?? {}),
      pollVoters: Map<String, List<String>>.from(
        (map['pollVoters'] ?? {}).map<String, List<String>>(
          (key, value) => MapEntry(key, List<String>.from(value ?? [])),
        ),
      ),
    );
  }

  Post copyWith({
    String? id,
    String? author,
    String? authorId,
    String? title,
    String? content,
    double? price,
    String? category,
    List<String>? tags,
    DateTime? createdAt,
    int? likes,
    int? views,
    bool? isPaid,
    bool? isPublic,
    bool? allowComments,
    String? imageUrl,
    List<String>? imageUrls,
    List<String>? videoUrls,
    String? linkUrl,
    List<String>? likedBy,
    bool? hasPoll,
    Map<String, int>? pollVotes,
    Map<String, List<String>>? pollVoters,
  }) {
    return Post(
      id: id ?? this.id,
      author: author ?? this.author,
      authorId: authorId ?? this.authorId,
      title: title ?? this.title,
      content: content ?? this.content,
      price: price ?? this.price,
      category: category ?? this.category,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      likes: likes ?? this.likes,
      views: views ?? this.views,
      isPaid: isPaid ?? this.isPaid,
      isPublic: isPublic ?? this.isPublic,
      allowComments: allowComments ?? this.allowComments,
      imageUrl: imageUrl ?? this.imageUrl,
      imageUrls: imageUrls ?? this.imageUrls,
      videoUrls: videoUrls ?? this.videoUrls,
      linkUrl: linkUrl ?? this.linkUrl,
      likedBy: likedBy ?? this.likedBy,
      hasPoll: hasPoll ?? this.hasPoll,
      pollVotes: pollVotes ?? this.pollVotes,
      pollVoters: pollVoters ?? this.pollVoters,
    );
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  String get formattedPrice {
    if (price == 0) return 'Free';
    return price.toStringAsFixed(2);
  }

  // Poll helper methods
  int get totalPollVotes {
    if (!hasPoll) return 0;
    return pollVotes.values.fold(0, (total, votes) => total + votes);
  }

  bool hasUserVoted(String userId) {
    if (!hasPoll) return false;
    return pollVoters.values.any((voters) => voters.contains(userId));
  }

  String? getUserVote(String userId) {
    if (!hasPoll) return null;
    for (final entry in pollVoters.entries) {
      if (entry.value.contains(userId)) {
        return entry.key;
      }
    }
    return null;
  }

  double getVotePercentage(String option) {
    if (!hasPoll || totalPollVotes == 0) return 0.0;
    final votes = pollVotes[option] ?? 0;
    return (votes / totalPollVotes) * 100;
  }
}

class PostService {
  static final PostService _instance = PostService._internal();
  factory PostService() => _instance;
  PostService._internal() {
    _postsController = StreamController<List<Post>>.broadcast();
  }

  final List<Post> _posts = [];
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? _postsSubscription;
  late final StreamController<List<Post>> _postsController;

  bool _isInitialized = false;
  bool _isInitializing = false;
  Completer<void>? _initializationCompleter;

  // Real-time configuration
  static const int _initialLoadLimit = 50; // Initial load for better UX

  // Pagination support for older posts (separate from real-time)
  DocumentSnapshot? _lastDocument;
  bool _hasMorePosts = true;
  bool _isLoadingMore = false;

  // Real-time listener management
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>?
      _realtimeSubscription;

  // Debouncing for user interactions
  final Map<String, Debouncer> _debouncers = {};
  final RateLimiter _likeRateLimiter = RateLimiter(
    maxRequests: 10,
    timeWindow: const Duration(minutes: 1),
  );

  String get _currentUserId => _auth.currentUser?.uid ?? 'anonymous';
  String get _currentUserName =>
      _auth.currentUser?.displayName ??
      _auth.currentUser?.email ??
      'Anonymous User';

  // Stream getter for real-time posts
  Stream<List<Post>> get postsStream {
    return Stream.multi((controller) {
      // Emit current posts immediately if available
      if (_posts.isNotEmpty) {
        debugPrint(
            'PostService: Emitting ${_posts.length} current posts to new listener');
        controller.add(List.unmodifiable(_posts));
      }

      // Listen to future updates
      final subscription = _postsController.stream.listen(
        (posts) {
          controller.add(posts);
        },
        onError: controller.addError,
        onDone: controller.close,
      );

      controller.onCancel = () {
        subscription.cancel();
      };
    });
  }

  // Getter to check if service is initialized
  bool get isInitialized => _isInitialized;

  // Future that completes when service is initialized
  Future<void> get initialized =>
      _initializationCompleter?.future ?? Future.value();

  // Get filtered and sorted posts as a stream
  Stream<List<Post>> getPostsStreamSortedBy(String sortBy, {String? category}) {
    return postsStream.map((posts) {
      var filteredPosts = category == null || category == 'All'
          ? List<Post>.from(posts)
          : posts.where((post) => post.category == category).toList();

      switch (sortBy) {
        case 'Highest Paid':
          filteredPosts.sort((a, b) {
            final priceComparison = b.price.compareTo(a.price);
            if (priceComparison != 0) return priceComparison;
            // Same price, sort by creation date (latest first)
            return b.createdAt.compareTo(a.createdAt);
          });
          break;
        case 'Most Popular':
          filteredPosts.sort((a, b) {
            final likesComparison = b.likes.compareTo(a.likes);
            if (likesComparison != 0) return likesComparison;
            // Same likes, sort by creation date (latest first)
            return b.createdAt.compareTo(a.createdAt);
          });
          break;
        case 'Recent':
          filteredPosts.sort((a, b) => b.createdAt.compareTo(a.createdAt));
          break;
        case 'Most Views':
          filteredPosts.sort((a, b) {
            final viewsComparison = b.views.compareTo(a.views);
            if (viewsComparison != 0) return viewsComparison;
            // Same views, sort by creation date (latest first)
            return b.createdAt.compareTo(a.createdAt);
          });
          break;
      }

      return filteredPosts;
    });
  }

  Future<void> initialize() async {
    if (_isInitialized) {
      return;
    }

    if (_isInitializing) {
      return _initializationCompleter?.future ?? Future.value();
    }

    _isInitializing = true;
    _initializationCompleter = Completer<void>();

    try {
      _listenToPosts();

      _isInitialized = true;
      _initializationCompleter?.complete();
    } catch (e) {
      _initializationCompleter?.completeError(e);
      rethrow;
    } finally {
      _isInitializing = false;
    }
  }

  void _listenToPosts() {
    _postsSubscription?.cancel();
    _realtimeSubscription?.cancel();

    // Set up comprehensive real-time listener WITHOUT pagination limits
    _realtimeSubscription = _firestore
        .collection('posts')
        .orderBy('createdAt', descending: true)
        // NO LIMIT - Listen to ALL posts for true real-time functionality
        .snapshots()
        .listen(
      (snapshot) {
        _handleRealtimeUpdate(snapshot);
        debugPrint('PostService: Real-time update - ${_posts.length} posts');
      },
      onError: (error) {
        debugPrint('PostService: Error in real-time listener: $error');
        _postsController.addError(error);
      },
    );
  }

  /// Handle real-time updates with smart memory management and automatic insertion
  void _handleRealtimeUpdate(QuerySnapshot<Map<String, dynamic>> snapshot) {
    final newPosts = snapshot.docs.map((doc) {
      final data = doc.data();
      final map = Map<String, dynamic>.from(data)..['id'] = doc.id;
      return Post.fromMap(map);
    }).toList();

    // Detect new posts by comparing with existing posts
    final existingIds = _posts.map((p) => p.id).toSet();
    final trulyNewPosts =
        newPosts.where((p) => !existingIds.contains(p.id)).toList();

    if (trulyNewPosts.isNotEmpty) {
      debugPrint(
          'PostService: ${trulyNewPosts.length} new posts detected in real-time');

      // Insert new posts at the beginning (most recent first)
      _insertNewPostsAtTop(trulyNewPosts);
    }

    // Handle updates to existing posts
    _updateExistingPosts(newPosts);

    // Update pagination state for older posts
    if (snapshot.docs.isNotEmpty) {
      _lastDocument = snapshot.docs.last;
      _hasMorePosts = newPosts.length >= _initialLoadLimit;
    } else {
      _hasMorePosts = false;
    }

    // Emit updated posts to stream
    _postsController.add(List.unmodifiable(_posts));
  }

  /// Insert new posts and maintain proper sorting
  void _insertNewPostsAtTop(List<Post> newPosts) {
    // Add new posts to the list
    _posts.addAll(newPosts);

    // Sort the entire list by creation date (newest first) for real-time experience
    // The actual sorting by price/popularity will be handled by the controller
    _posts.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    debugPrint(
        'PostService: Added ${newPosts.length} new posts and sorted by creation date');
  }

  /// Update existing posts with new data (likes, views, etc.)
  void _updateExistingPosts(List<Post> allPosts) {
    final postMap = {for (var post in allPosts) post.id: post};

    for (int i = 0; i < _posts.length; i++) {
      final existingPost = _posts[i];
      final updatedPost = postMap[existingPost.id];

      if (updatedPost != null && _hasPostChanged(existingPost, updatedPost)) {
        _posts[i] = updatedPost;
        debugPrint(
            'PostService: Updated post ${existingPost.id} with new data');
      }
    }
  }

  /// Check if a post has meaningful changes (likes, views, etc.)
  bool _hasPostChanged(Post oldPost, Post newPost) {
    return oldPost.likes != newPost.likes ||
        oldPost.views != newPost.views ||
        oldPost.isPaid != newPost.isPaid ||
        oldPost.likedBy.length != newPost.likedBy.length;
  }

  Future<String> createPost({
    String? title,
    required String content,
    required double price,
    required String category,
    List<String>? tags,
    bool isPublic = true,
    bool allowComments = true,
    String? imageUrl, // Keep for backward compatibility
    List<String>? imageUrls, // New field for multiple images
    List<String>? videoUrls, // New field for videos
    String? linkUrl,
    bool hasPoll = false, // New field for poll
  }) async {
    // Initialize poll data if poll is enabled
    Map<String, int> pollVotes = {};
    Map<String, List<String>> pollVoters = {};

    if (hasPoll) {
      pollVotes = {
        'yes': 0,
        'no': 0,
        'dont_care': 0,
      };
      pollVoters = {
        'yes': <String>[],
        'no': <String>[],
        'dont_care': <String>[],
      };
    }

    final docRef = await _firestore.collection('posts').add({
      'author': _currentUserName,
      'authorId': _currentUserId,
      'title': title?.trim().isEmpty == true ? null : title?.trim(),
      'content': content.trim(),
      'price': price,
      'category': category,
      'tags': tags ?? [],
      'createdAt': FieldValue.serverTimestamp(),
      'likes': 0,
      'comments': 0,
      'views': 0,
      'isPaid': false,
      'isPublic': isPublic,
      'allowComments': allowComments,
      'imageUrl': imageUrl, // Keep for backward compatibility
      'imageUrls': imageUrls ?? [], // Multiple images
      'videoUrls': videoUrls ?? [], // Multiple videos
      'linkUrl': linkUrl,
      'hasPoll': hasPoll,
      'pollVotes': pollVotes,
      'pollVoters': pollVoters,
    });

    return docRef.id;
  }

  List<Post> getAllPosts() {
    return List.unmodifiable(_posts);
  }

  /// Get all posts, ensuring service is initialized first
  Future<List<Post>> getAllPostsAsync() async {
    if (!_isInitialized) {
      await initialized;
    }
    return List.unmodifiable(_posts);
  }

  /// Force refresh posts from Firestore
  Future<void> refreshPosts() async {
    try {
      // Reset pagination state
      _lastDocument = null;
      _hasMorePosts = true;
      _isLoadingMore = false;

      // Cancel existing subscriptions and restart with fresh listener
      _postsSubscription?.cancel();
      _realtimeSubscription?.cancel();
      _listenToPosts();

      debugPrint('PostService: Refreshed real-time posts listener');
    } catch (e) {
      debugPrint('PostService: Error refreshing posts: $e');
      rethrow;
    }
  }

  /// Load more posts for pagination (separate from real-time)
  Future<bool> loadMorePosts() async {
    if (_isLoadingMore || !_hasMorePosts || _lastDocument == null) {
      return false;
    }

    _isLoadingMore = true;

    try {
      final query = _firestore
          .collection('posts')
          .orderBy('createdAt', descending: true)
          .startAfterDocument(_lastDocument!)
          .limit(_initialLoadLimit);

      final snapshot = await query.get();

      if (snapshot.docs.isNotEmpty) {
        final newPosts = snapshot.docs.map((doc) {
          final data = doc.data();
          final map = Map<String, dynamic>.from(data)..['id'] = doc.id;
          return Post.fromMap(map);
        }).toList();

        // Add new posts to existing list (avoiding duplicates)
        final existingIds = _posts.map((p) => p.id).toSet();
        final uniqueNewPosts =
            newPosts.where((p) => !existingIds.contains(p.id)).toList();

        _posts.addAll(uniqueNewPosts);
        _lastDocument = snapshot.docs.last;
        _hasMorePosts = snapshot.docs.length == _initialLoadLimit;

        // Emit updated posts to stream
        _postsController.add(List.unmodifiable(_posts));

        debugPrint(
          'PostService: Loaded ${uniqueNewPosts.length} more posts. Total: ${_posts.length}',
        );
        return uniqueNewPosts.isNotEmpty;
      } else {
        _hasMorePosts = false;
        return false;
      }
    } catch (e) {
      debugPrint('PostService: Error loading more posts: $e');
      return false;
    } finally {
      _isLoadingMore = false;
    }
  }

  /// Check if more posts can be loaded
  bool get canLoadMore => _hasMorePosts && !_isLoadingMore;

  /// Check if currently loading more posts
  bool get isLoadingMore => _isLoadingMore;

  /// Get real-time posts stream with hybrid pagination support
  Stream<List<Post>> getRealtimePostsWithPagination({String? category}) {
    return postsStream.map((posts) {
      // Filter by category if specified
      var filteredPosts = category == null || category == 'All'
          ? List<Post>.from(posts)
          : posts.where((post) => post.category == category).toList();

      // Sort by creation date (newest first) for real-time experience
      filteredPosts.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return filteredPosts;
    });
  }

  /// Check if Firestore indexes are properly configured
  Future<bool> checkFirestoreIndexes() async {
    try {
      // Test the problematic query to see if index exists
      await _firestore
          .collection('posts')
          .where('category', isEqualTo: 'Politics')
          .orderBy('createdAt', descending: true)
          .limit(1)
          .get();

      debugPrint('PostService: Firestore indexes are properly configured');
      return true;
    } catch (e) {
      debugPrint('PostService: Firestore index missing. Error: $e');
      debugPrint(
          'PostService: Please create composite index: category (ASC), createdAt (DESC)');
      return false;
    }
  }

  /// Load historical posts (older than current posts) for pagination
  Future<List<Post>> loadHistoricalPosts(
      {String? category, int limit = 20}) async {
    try {
      // Get the oldest post's timestamp from current posts
      DateTime? oldestTimestamp;
      if (_posts.isNotEmpty) {
        oldestTimestamp = _posts
            .map((p) => p.createdAt)
            .reduce((a, b) => a.isBefore(b) ? a : b);
      }

      // SIMPLIFIED QUERY: Avoid composite index requirement
      // We'll fetch more posts and filter client-side to avoid index issues
      Query<Map<String, dynamic>> query;

      if (category != null && category != 'All') {
        // If category is specified, use category filter only (requires simple index)
        query = _firestore
            .collection('posts')
            .where('category', isEqualTo: category)
            .orderBy('createdAt', descending: true)
            .limit(limit * 2); // Get more to account for filtering
      } else {
        // If no category, use timestamp filter only
        query = _firestore
            .collection('posts')
            .orderBy('createdAt', descending: true);

        if (oldestTimestamp != null) {
          query = query.where('createdAt', isLessThan: oldestTimestamp);
        }

        query = query.limit(limit);
      }

      final snapshot = await query.get();

      var historicalPosts = snapshot.docs.map((doc) {
        final data = doc.data();
        final map = Map<String, dynamic>.from(data)..['id'] = doc.id;
        return Post.fromMap(map);
      }).toList();

      // Client-side filtering for category + timestamp combination
      if (category != null && category != 'All' && oldestTimestamp != null) {
        historicalPosts = historicalPosts
            .where((post) => post.createdAt.isBefore(oldestTimestamp!))
            .take(limit)
            .toList();
      }

      debugPrint(
          'PostService: Loaded ${historicalPosts.length} historical posts (category: $category)');
      return historicalPosts;
    } catch (e) {
      debugPrint('PostService: Error loading historical posts: $e');
      // Fallback: Return empty list to prevent app crashes
      return [];
    }
  }

  List<Post> getPostsByCategory(String category) {
    if (category == 'All') return getAllPosts();
    return _posts.where((post) => post.category == category).toList();
  }

  List<Post> getPostsSortedBy(String sortBy, {String? category}) {
    var posts = category == null || category == 'All'
        ? List<Post>.from(_posts)
        : getPostsByCategory(category);

    switch (sortBy) {
      case 'Highest Paid':
        posts.sort((a, b) {
          final priceComparison = b.price.compareTo(a.price);
          if (priceComparison != 0) return priceComparison;
          // Same price, sort by creation date (latest first)
          return b.createdAt.compareTo(a.createdAt);
        });
        break;
      case 'Most Popular':
        posts.sort((a, b) {
          final likesComparison = b.likes.compareTo(a.likes);
          if (likesComparison != 0) return likesComparison;
          // Same likes, sort by creation date (latest first)
          return b.createdAt.compareTo(a.createdAt);
        });
        break;
      case 'Recent':
        posts.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case 'Most Views':
        posts.sort((a, b) {
          final viewsComparison = b.views.compareTo(a.views);
          if (viewsComparison != 0) return viewsComparison;
          // Same views, sort by creation date (latest first)
          return b.createdAt.compareTo(a.createdAt);
        });
        break;
    }

    return posts;
  }

  // // DEPRECATED: Use PostController.getTopPaidPostForCategory() instead
  // // This method is kept for backward compatibility but should not be used
  // @Deprecated('Use PostController.getTopPaidPostForCategory() instead')
  // Post? getTopPaidPostForCategory(String category) {
  //   debugPrint(
  //       'PostService.getTopPaidPostForCategory is deprecated. Use PostController instead.');
  //   final categoryPosts = getPostsByCategory(category);
  //   if (categoryPosts.isEmpty) return null;

  //   // Simple fallback: return highest priced post
  //   categoryPosts.sort((a, b) {
  //     final priceComparison = b.price.compareTo(a.price);
  //     if (priceComparison != 0) return priceComparison;
  //     return b.createdAt.compareTo(a.createdAt);
  //   });

  //   return categoryPosts.first;
  // }

  // // DEPRECATED: Use PostController methods instead
  // @Deprecated(
  //     'Use PostController.getTopPaidPostForCategory() for each category instead')
  // Map<String, Post?> getTopPaidPostsForAllCategories() {
  //   final categories = [
  //     'Politics',
  //     'News',
  //     'Sports',
  //     'Entertainment',
  //     'Sex',
  //     'Religion',
  //   ];
  //   final Map<String, Post?> topPosts = {};

  //   for (final category in categories) {
  //     // Use the deprecated method for backward compatibility
  //     // ignore: deprecated_member_use
  //     topPosts[category] = getTopPaidPostForCategory(category);
  //   }

  //   return topPosts;
  // }

  Post? getPostById(String id) {
    try {
      return _posts.firstWhere((post) => post.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<void> likePost(String postId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return;

    // Rate limiting to prevent spam
    if (!_likeRateLimiter.tryRequest()) {
      debugPrint('PostService: Like rate limit exceeded');
      return;
    }

    // Debounce like actions to prevent rapid tapping
    final debounceKey = 'like_$postId';
    _debouncers[debounceKey]?.cancel();
    _debouncers[debounceKey] = Debouncer(
      delay: const Duration(milliseconds: 300),
    );

    _debouncers[debounceKey]!.call(() async {
      try {
        final postRef = _firestore.collection('posts').doc(postId);

        await _firestore.runTransaction((transaction) async {
          final postDoc = await transaction.get(postRef);
          if (!postDoc.exists) return;

          final data = postDoc.data()!;
          final likedBy = List<String>.from(data['likedBy'] ?? []);
          final currentLikes = data['likes'] ?? 0;

          if (likedBy.contains(currentUser.uid)) {
            // Unlike
            likedBy.remove(currentUser.uid);
            transaction.update(postRef, {
              'likedBy': likedBy,
              'likes': currentLikes - 1,
            });
          } else {
            // Like
            likedBy.add(currentUser.uid);
            transaction.update(postRef, {
              'likedBy': likedBy,
              'likes': currentLikes + 1,
            });
          }
        });
      } catch (e) {
        debugPrint('PostService: Error liking post: $e');
      }
    });
  }

  Future<void> viewPost(String postId) async {
    await _firestore.collection('posts').doc(postId).update({
      'views': FieldValue.increment(1),
    });
  }

  /// Vote on a poll
  Future<void> voteOnPoll(String postId, String option) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return;

    final userId = currentUser.uid;
    final postDoc = _firestore.collection('posts').doc(postId);

    await _firestore.runTransaction((transaction) async {
      final snapshot = await transaction.get(postDoc);
      if (!snapshot.exists) return;

      final data = snapshot.data()!;
      final hasPoll = data['hasPoll'] ?? false;
      if (!hasPoll) return;

      final pollVotes = Map<String, int>.from(data['pollVotes'] ?? {});
      final pollVoters = Map<String, List<String>>.from(
        (data['pollVoters'] ?? {}).map<String, List<String>>(
          (key, value) => MapEntry(key, List<String>.from(value ?? [])),
        ),
      );

      // Remove user from any existing votes
      for (final key in pollVoters.keys) {
        pollVoters[key]!.remove(userId);
        pollVotes[key] = pollVoters[key]!.length;
      }

      // Add user to the new option
      if (!pollVoters.containsKey(option)) {
        pollVoters[option] = <String>[];
        pollVotes[option] = 0;
      }
      pollVoters[option]!.add(userId);
      pollVotes[option] = pollVoters[option]!.length;

      // Update the document
      transaction.update(postDoc, {
        'pollVotes': pollVotes,
        'pollVoters': pollVoters,
      });
    });
  }

  Future<void> deletePost(String postId) async {
    final doc = _firestore.collection('posts').doc(postId);
    final snapshot = await doc.get();
    if (snapshot.exists && snapshot['authorId'] == _currentUserId) {
      await doc.delete();
    }
  }

  List<Post> getUserPosts() {
    return _posts.where((post) => post.authorId == _currentUserId).toList();
  }

  List<String> getCategories() {
    // Use standardized Categories class
    return Categories.namesWithAll;
  }

  List<String> getSortOptions() {
    return ['Highest Paid', 'Most Popular', 'Recent', 'Most Views'];
  }

  // Statistics
  int get totalPosts => _posts.length;
  int get userPostsCount => getUserPosts().length;

  double get averagePostPrice {
    if (_posts.isEmpty) return 0.0;
    return _posts.map((p) => p.price).reduce((a, b) => a + b) / _posts.length;
  }

  double get totalValueInPosts {
    return _posts.map((p) => p.price).fold(0.0, (a, b) => a + b);
  }

  Map<String, int> get postsByCategory {
    final Map<String, int> categoryCount = {};
    for (final post in _posts) {
      categoryCount[post.category] = (categoryCount[post.category] ?? 0) + 1;
    }
    return categoryCount;
  }

  void dispose() {
    _postsSubscription?.cancel();
    _realtimeSubscription?.cancel();
    _postsController.close();

    // Cancel all debouncers
    for (final debouncer in _debouncers.values) {
      debouncer.cancel();
    }
    _debouncers.clear();
  }
}
